{"version": 3, "sources": ["../../builds/worker.ts", "../../src/analyticsClient.ts"], "sourcesContent": ["// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nexport type AnalyticsClient = ReturnType<typeof createAnalyticsClient>;\n\nimport { createMemoryCache, createNullCache, createNullLogger } from '@algolia/client-common';\nimport { createFetchRequester } from '@algolia/requester-fetch';\n\nimport type { ClientOptions } from '@algolia/client-common';\n\nimport { createAnalyticsClient } from '../src/analyticsClient';\n\nimport type { Region } from '../src/analyticsClient';\nimport { REGIONS } from '../src/analyticsClient';\n\nexport type { Region, RegionOptions } from '../src/analyticsClient';\n\nexport { apiClientVersion } from '../src/analyticsClient';\n\nexport * from '../model';\n\nexport function analyticsClient(\n  appId: string,\n  apiKey: string,\n  region?: Region,\n  options?: ClientOptions,\n): AnalyticsClient {\n  if (!appId || typeof appId !== 'string') {\n    throw new Error('`appId` is missing.');\n  }\n\n  if (!apiKey || typeof apiKey !== 'string') {\n    throw new Error('`apiKey` is missing.');\n  }\n\n  if (region && (typeof region !== 'string' || !REGIONS.includes(region))) {\n    throw new Error(`\\`region\\` must be one of the following: ${REGIONS.join(', ')}`);\n  }\n\n  return {\n    ...createAnalyticsClient({\n      appId,\n      apiKey,\n      region,\n      timeouts: {\n        connect: 2000,\n        read: 5000,\n        write: 30000,\n      },\n      logger: createNullLogger(),\n      requester: createFetchRequester(),\n      algoliaAgents: [{ segment: 'Worker' }],\n      responsesCache: createNullCache(),\n      requestsCache: createNullCache(),\n      hostsCache: createMemoryCache(),\n      ...options,\n    }),\n  };\n}\n", "// Code generated by OpenAPI Generator (https://openapi-generator.tech), manual changes will be lost - read more on https://github.com/algolia/api-clients-automation. DO NOT EDIT.\n\nimport type {\n  CreateClientOptions,\n  Headers,\n  Host,\n  QueryParameters,\n  Request,\n  RequestOptions,\n} from '@algolia/client-common';\nimport { createAuth, createTransporter, getAlgoliaAgent } from '@algolia/client-common';\n\nimport type { GetAddToCartRateResponse } from '../model/getAddToCartRateResponse';\nimport type { GetAverageClickPositionResponse } from '../model/getAverageClickPositionResponse';\nimport type { GetClickPositionsResponse } from '../model/getClickPositionsResponse';\nimport type { GetClickThroughRateResponse } from '../model/getClickThroughRateResponse';\nimport type { GetConversionRateResponse } from '../model/getConversionRateResponse';\nimport type { GetNoClickRateResponse } from '../model/getNoClickRateResponse';\nimport type { GetNoResultsRateResponse } from '../model/getNoResultsRateResponse';\nimport type { GetPurchaseRateResponse } from '../model/getPurchaseRateResponse';\nimport type { GetRevenue } from '../model/getRevenue';\nimport type { GetSearchesCountResponse } from '../model/getSearchesCountResponse';\nimport type { GetSearchesNoClicksResponse } from '../model/getSearchesNoClicksResponse';\nimport type { GetSearchesNoResultsResponse } from '../model/getSearchesNoResultsResponse';\nimport type { GetStatusResponse } from '../model/getStatusResponse';\nimport type { GetTopCountriesResponse } from '../model/getTopCountriesResponse';\nimport type { GetTopFilterAttributesResponse } from '../model/getTopFilterAttributesResponse';\nimport type { GetTopFilterForAttributeResponse } from '../model/getTopFilterForAttributeResponse';\nimport type { GetTopFiltersNoResultsResponse } from '../model/getTopFiltersNoResultsResponse';\nimport type { GetTopHitsResponse } from '../model/getTopHitsResponse';\nimport type { GetTopSearchesResponse } from '../model/getTopSearchesResponse';\nimport type { GetUsersCountResponse } from '../model/getUsersCountResponse';\n\nimport type {\n  CustomDeleteProps,\n  CustomGetProps,\n  CustomPostProps,\n  CustomPutProps,\n  GetAddToCartRateProps,\n  GetAverageClickPositionProps,\n  GetClickPositionsProps,\n  GetClickThroughRateProps,\n  GetConversionRateProps,\n  GetNoClickRateProps,\n  GetNoResultsRateProps,\n  GetPurchaseRateProps,\n  GetRevenueProps,\n  GetSearchesCountProps,\n  GetSearchesNoClicksProps,\n  GetSearchesNoResultsProps,\n  GetStatusProps,\n  GetTopCountriesProps,\n  GetTopFilterAttributesProps,\n  GetTopFilterForAttributeProps,\n  GetTopFiltersNoResultsProps,\n  GetTopHitsProps,\n  GetTopSearchesProps,\n  GetUsersCountProps,\n} from '../model/clientMethodProps';\n\nexport const apiClientVersion = '5.20.0';\n\nexport const REGIONS = ['de', 'us'] as const;\nexport type Region = (typeof REGIONS)[number];\nexport type RegionOptions = { region?: Region };\n\nfunction getDefaultHosts(region?: Region): Host[] {\n  const url = !region ? 'analytics.algolia.com' : 'analytics.{region}.algolia.com'.replace('{region}', region);\n\n  return [{ url, accept: 'readWrite', protocol: 'https' }];\n}\n\nexport function createAnalyticsClient({\n  appId: appIdOption,\n  apiKey: apiKeyOption,\n  authMode,\n  algoliaAgents,\n  region: regionOption,\n  ...options\n}: CreateClientOptions & RegionOptions) {\n  const auth = createAuth(appIdOption, apiKeyOption, authMode);\n  const transporter = createTransporter({\n    hosts: getDefaultHosts(regionOption),\n    ...options,\n    algoliaAgent: getAlgoliaAgent({\n      algoliaAgents,\n      client: 'Analytics',\n      version: apiClientVersion,\n    }),\n    baseHeaders: {\n      'content-type': 'text/plain',\n      ...auth.headers(),\n      ...options.baseHeaders,\n    },\n    baseQueryParameters: {\n      ...auth.queryParameters(),\n      ...options.baseQueryParameters,\n    },\n  });\n\n  return {\n    transporter,\n\n    /**\n     * The `appId` currently in use.\n     */\n    appId: appIdOption,\n\n    /**\n     * The `apiKey` currently in use.\n     */\n    apiKey: apiKeyOption,\n\n    /**\n     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.\n     */\n    clearCache(): Promise<void> {\n      return Promise.all([transporter.requestsCache.clear(), transporter.responsesCache.clear()]).then(() => undefined);\n    },\n\n    /**\n     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.\n     */\n    get _ua(): string {\n      return transporter.algoliaAgent.value;\n    },\n\n    /**\n     * Adds a `segment` to the `x-algolia-agent` sent with every requests.\n     *\n     * @param segment - The algolia agent (user-agent) segment to add.\n     * @param version - The version of the agent.\n     */\n    addAlgoliaAgent(segment: string, version?: string): void {\n      transporter.algoliaAgent.add({ segment, version });\n    },\n\n    /**\n     * Helper method to switch the API key used to authenticate the requests.\n     *\n     * @param params - Method params.\n     * @param params.apiKey - The new API Key to use.\n     */\n    setClientApiKey({ apiKey }: { apiKey: string }): void {\n      if (!authMode || authMode === 'WithinHeaders') {\n        transporter.baseHeaders['x-algolia-api-key'] = apiKey;\n      } else {\n        transporter.baseQueryParameters['x-algolia-api-key'] = apiKey;\n      }\n    },\n\n    /**\n     * This method allow you to send requests to the Algolia REST API.\n     * @param customDelete - The customDelete object.\n     * @param customDelete.path - Path of the endpoint, anything after \\\"/1\\\" must be specified.\n     * @param customDelete.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customDelete(\n      { path, parameters }: CustomDeleteProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customDelete`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'DELETE',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method allow you to send requests to the Algolia REST API.\n     * @param customGet - The customGet object.\n     * @param customGet.path - Path of the endpoint, anything after \\\"/1\\\" must be specified.\n     * @param customGet.parameters - Query parameters to apply to the current query.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customGet`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method allow you to send requests to the Algolia REST API.\n     * @param customPost - The customPost object.\n     * @param customPost.path - Path of the endpoint, anything after \\\"/1\\\" must be specified.\n     * @param customPost.parameters - Query parameters to apply to the current query.\n     * @param customPost.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPost(\n      { path, parameters, body }: CustomPostProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPost`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'POST',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * This method allow you to send requests to the Algolia REST API.\n     * @param customPut - The customPut object.\n     * @param customPut.path - Path of the endpoint, anything after \\\"/1\\\" must be specified.\n     * @param customPut.parameters - Query parameters to apply to the current query.\n     * @param customPut.body - Parameters to send with the custom request.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    customPut(\n      { path, parameters, body }: CustomPutProps,\n      requestOptions?: RequestOptions,\n    ): Promise<Record<string, unknown>> {\n      if (!path) {\n        throw new Error('Parameter `path` is required when calling `customPut`.');\n      }\n\n      const requestPath = '/{path}'.replace('{path}', path);\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = parameters ? parameters : {};\n\n      const request: Request = {\n        method: 'PUT',\n        path: requestPath,\n        queryParameters,\n        headers,\n        data: body ? body : {},\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the add-to-cart rate for all your searches with at least one add-to-cart event, including a daily breakdown.  By default, the analyzed period includes the last eight days including the current day.  The rate is the number of add-to-cart conversion events divided by the number of tracked searches. A search is tracked if it returns a queryID (`clickAnalytics` is `true`). This differs from the response\\'s `count`, which shows the overall number of searches, including those where `clickAnalytics` is `false`.  **There\\'s a difference between a 0 and null add-to-cart rate when `clickAnalytics` is enabled:**  - **Null** means there were no queries: since Algolia didn\\'t receive any events, the add-to-cart rate is null. - **0** mean there _were_ queries but no [add-to-cart events](https://www.algolia.com/doc/guides/sending-events/getting-started/) were received.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getAddToCartRate - The getAddToCartRate object.\n     * @param getAddToCartRate.index - Index name.\n     * @param getAddToCartRate.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getAddToCartRate.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getAddToCartRate.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getAddToCartRate(\n      { index, startDate, endDate, tags }: GetAddToCartRateProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetAddToCartRateResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getAddToCartRate`.');\n      }\n\n      const requestPath = '/2/conversions/addToCartRate';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the average click position of your search results, including a daily breakdown.  The average click position is the average of all clicked search result positions. For example, if users only ever click on the first result for any search, the average click position is 1. By default, the analyzed period includes the last eight days including the current day.  An average of `null` when `clickAnalytics` is enabled means Algolia didn\\'t receive any [click events](https://www.algolia.com/doc/guides/sending-events/getting-started/) for the queries. The average is `null` until Algolia receives at least one click event.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getAverageClickPosition - The getAverageClickPosition object.\n     * @param getAverageClickPosition.index - Index name.\n     * @param getAverageClickPosition.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getAverageClickPosition.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getAverageClickPosition.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getAverageClickPosition(\n      { index, startDate, endDate, tags }: GetAverageClickPositionProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetAverageClickPositionResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getAverageClickPosition`.');\n      }\n\n      const requestPath = '/2/clicks/averageClickPosition';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the positions in the search results and their associated number of clicks.  This lets you check how many clicks the first, second, or tenth search results receive.  An average of `0` when `clickAnalytics` is enabled means Algolia didn\\'t receive any [click events](https://www.algolia.com/doc/guides/sending-events/getting-started/) for the queries.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getClickPositions - The getClickPositions object.\n     * @param getClickPositions.index - Index name.\n     * @param getClickPositions.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getClickPositions.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getClickPositions.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getClickPositions(\n      { index, startDate, endDate, tags }: GetClickPositionsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetClickPositionsResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getClickPositions`.');\n      }\n\n      const requestPath = '/2/clicks/positions';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the click-through rate (CTR) for all your searches with at least one click event, including a daily breakdown.  By default, the analyzed period includes the last eight days including the current day.  **There\\'s a difference between a 0 and null CTR when `clickAnalytics` is enabled:**  - **Null** means there were no queries: since Algolia didn\\'t receive any events, CTR is null. - **0** mean there _were_ queries but no [click events](https://www.algolia.com/doc/guides/sending-events/getting-started/) were received.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getClickThroughRate - The getClickThroughRate object.\n     * @param getClickThroughRate.index - Index name.\n     * @param getClickThroughRate.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getClickThroughRate.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getClickThroughRate.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getClickThroughRate(\n      { index, startDate, endDate, tags }: GetClickThroughRateProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetClickThroughRateResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getClickThroughRate`.');\n      }\n\n      const requestPath = '/2/clicks/clickThroughRate';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the conversion rate (CR) for all your searches with at least one conversion event, including a daily breakdown.  By default, the analyzed period includes the last eight days including the current day.  **There\\'s a difference between a 0 and null CR when `clickAnalytics` is enabled:**  - **Null** means there were no queries: since Algolia didn\\'t receive any events, CR is null. - **0** mean there _were_ queries but no [conversion events](https://www.algolia.com/doc/guides/sending-events/getting-started/) were received.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getConversionRate - The getConversionRate object.\n     * @param getConversionRate.index - Index name.\n     * @param getConversionRate.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getConversionRate.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getConversionRate.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getConversionRate(\n      { index, startDate, endDate, tags }: GetConversionRateProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetConversionRateResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getConversionRate`.');\n      }\n\n      const requestPath = '/2/conversions/conversionRate';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the fraction of searches that didn\\'t lead to any click within a time range, including a daily breakdown. It also returns the number of tracked searches and tracked searches without clicks.  By default, the analyzed period includes the last eight days including the current day.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getNoClickRate - The getNoClickRate object.\n     * @param getNoClickRate.index - Index name.\n     * @param getNoClickRate.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getNoClickRate.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getNoClickRate.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getNoClickRate(\n      { index, startDate, endDate, tags }: GetNoClickRateProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetNoClickRateResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getNoClickRate`.');\n      }\n\n      const requestPath = '/2/searches/noClickRate';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the fraction of searches that didn\\'t return any results within a time range, including a daily breakdown. It also returns the count of searches and searches without results used to compute the rates.  By default, the analyzed period includes the last eight days including the current day.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getNoResultsRate - The getNoResultsRate object.\n     * @param getNoResultsRate.index - Index name.\n     * @param getNoResultsRate.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getNoResultsRate.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getNoResultsRate.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getNoResultsRate(\n      { index, startDate, endDate, tags }: GetNoResultsRateProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetNoResultsRateResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getNoResultsRate`.');\n      }\n\n      const requestPath = '/2/searches/noResultRate';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the purchase rate for all your searches with at least one purchase event, including a daily breakdown.  By default, the analyzed period includes the last eight days including the current day.  The rate is the number of purchase conversion events divided by the number of tracked searches. A search is tracked if it returns a query ID (`clickAnalytics` is `true`). This differs from the response\\'s `count`, which shows the overall number of searches, including those where `clickAnalytics` is `false`.  **There\\'s a difference between a 0 and null purchase rate when `clickAnalytics` is enabled:**  - **Null** means there were no queries: since Algolia didn\\'t receive any events, the purchase rate is null. - **0** mean there _were_ queries but no [purchase conversion events](https://www.algolia.com/doc/guides/sending-events/getting-started/) were received.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getPurchaseRate - The getPurchaseRate object.\n     * @param getPurchaseRate.index - Index name.\n     * @param getPurchaseRate.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getPurchaseRate.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getPurchaseRate.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getPurchaseRate(\n      { index, startDate, endDate, tags }: GetPurchaseRateProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetPurchaseRateResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getPurchaseRate`.');\n      }\n\n      const requestPath = '/2/conversions/purchaseRate';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves revenue-related metrics, such as the total revenue or the average order value.  To retrieve revenue-related metrics, send purchase events. By default, the analyzed period includes the last eight days including the current day.  Revenue is based on purchase conversion events (a conversion event with an `eventSubtype` attribute of `purchase`). The revenue is the `price` attribute multiplied by the `quantity` attribute for each object in the event\\'s `objectData` array.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getRevenue - The getRevenue object.\n     * @param getRevenue.index - Index name.\n     * @param getRevenue.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getRevenue.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getRevenue.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getRevenue(\n      { index, startDate, endDate, tags }: GetRevenueProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetRevenue> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getRevenue`.');\n      }\n\n      const requestPath = '/2/conversions/revenue';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the number of searches within a time range, including a daily breakdown.  By default, the analyzed period includes the last eight days including the current day.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getSearchesCount - The getSearchesCount object.\n     * @param getSearchesCount.index - Index name.\n     * @param getSearchesCount.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getSearchesCount.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getSearchesCount.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getSearchesCount(\n      { index, startDate, endDate, tags }: GetSearchesCountProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetSearchesCountResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getSearchesCount`.');\n      }\n\n      const requestPath = '/2/searches/count';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the most popular searches that didn\\'t lead to any clicks, from the 1,000 most frequent searches.  For each search, it also returns the number of displayed search results that remained unclicked.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getSearchesNoClicks - The getSearchesNoClicks object.\n     * @param getSearchesNoClicks.index - Index name.\n     * @param getSearchesNoClicks.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getSearchesNoClicks.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getSearchesNoClicks.limit - Number of items to return.\n     * @param getSearchesNoClicks.offset - Position of the first item to return.\n     * @param getSearchesNoClicks.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getSearchesNoClicks(\n      { index, startDate, endDate, limit, offset, tags }: GetSearchesNoClicksProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetSearchesNoClicksResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getSearchesNoClicks`.');\n      }\n\n      const requestPath = '/2/searches/noClicks';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (limit !== undefined) {\n        queryParameters['limit'] = limit.toString();\n      }\n\n      if (offset !== undefined) {\n        queryParameters['offset'] = offset.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the 1,000 most frequent searches that produced zero results.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getSearchesNoResults - The getSearchesNoResults object.\n     * @param getSearchesNoResults.index - Index name.\n     * @param getSearchesNoResults.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getSearchesNoResults.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getSearchesNoResults.limit - Number of items to return.\n     * @param getSearchesNoResults.offset - Position of the first item to return.\n     * @param getSearchesNoResults.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getSearchesNoResults(\n      { index, startDate, endDate, limit, offset, tags }: GetSearchesNoResultsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetSearchesNoResultsResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getSearchesNoResults`.');\n      }\n\n      const requestPath = '/2/searches/noResults';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (limit !== undefined) {\n        queryParameters['limit'] = limit.toString();\n      }\n\n      if (offset !== undefined) {\n        queryParameters['offset'] = offset.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the time when the Analytics data for the specified index was last updated.  If the index has been recently created or no search has been performed yet the updated time is `null`.  The Analytics data is updated every 5&nbsp;minutes.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getStatus - The getStatus object.\n     * @param getStatus.index - Index name.\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getStatus({ index }: GetStatusProps, requestOptions?: RequestOptions): Promise<GetStatusResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getStatus`.');\n      }\n\n      const requestPath = '/2/status';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the countries with the most searches in your index.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getTopCountries - The getTopCountries object.\n     * @param getTopCountries.index - Index name.\n     * @param getTopCountries.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getTopCountries.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getTopCountries.limit - Number of items to return.\n     * @param getTopCountries.offset - Position of the first item to return.\n     * @param getTopCountries.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTopCountries(\n      { index, startDate, endDate, limit, offset, tags }: GetTopCountriesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetTopCountriesResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getTopCountries`.');\n      }\n\n      const requestPath = '/2/countries';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (limit !== undefined) {\n        queryParameters['limit'] = limit.toString();\n      }\n\n      if (offset !== undefined) {\n        queryParameters['offset'] = offset.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the 1,000 most frequently used filter attributes.  These are attributes of your records that you included in the `attributesForFaceting` setting.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getTopFilterAttributes - The getTopFilterAttributes object.\n     * @param getTopFilterAttributes.index - Index name.\n     * @param getTopFilterAttributes.search - Search query.\n     * @param getTopFilterAttributes.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getTopFilterAttributes.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getTopFilterAttributes.limit - Number of items to return.\n     * @param getTopFilterAttributes.offset - Position of the first item to return.\n     * @param getTopFilterAttributes.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTopFilterAttributes(\n      { index, search, startDate, endDate, limit, offset, tags }: GetTopFilterAttributesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetTopFilterAttributesResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getTopFilterAttributes`.');\n      }\n\n      const requestPath = '/2/filters';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (search !== undefined) {\n        queryParameters['search'] = search.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (limit !== undefined) {\n        queryParameters['limit'] = limit.toString();\n      }\n\n      if (offset !== undefined) {\n        queryParameters['offset'] = offset.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the 1,000 most frequent filter (facet) values for a filter attribute.  These are attributes of your records that you included in the `attributesForFaceting` setting.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getTopFilterForAttribute - The getTopFilterForAttribute object.\n     * @param getTopFilterForAttribute.attribute - Attribute name.\n     * @param getTopFilterForAttribute.index - Index name.\n     * @param getTopFilterForAttribute.search - Search query.\n     * @param getTopFilterForAttribute.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getTopFilterForAttribute.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getTopFilterForAttribute.limit - Number of items to return.\n     * @param getTopFilterForAttribute.offset - Position of the first item to return.\n     * @param getTopFilterForAttribute.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTopFilterForAttribute(\n      { attribute, index, search, startDate, endDate, limit, offset, tags }: GetTopFilterForAttributeProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetTopFilterForAttributeResponse> {\n      if (!attribute) {\n        throw new Error('Parameter `attribute` is required when calling `getTopFilterForAttribute`.');\n      }\n\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getTopFilterForAttribute`.');\n      }\n\n      const requestPath = '/2/filters/{attribute}'.replace('{attribute}', encodeURIComponent(attribute));\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (search !== undefined) {\n        queryParameters['search'] = search.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (limit !== undefined) {\n        queryParameters['limit'] = limit.toString();\n      }\n\n      if (offset !== undefined) {\n        queryParameters['offset'] = offset.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the 1,000 most frequently used filters for a search that didn\\'t return any results.  To get the most frequent searches without results, use the [Retrieve searches without results](#tag/search/operation/getSearchesNoResults) operation.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getTopFiltersNoResults - The getTopFiltersNoResults object.\n     * @param getTopFiltersNoResults.index - Index name.\n     * @param getTopFiltersNoResults.search - Search query.\n     * @param getTopFiltersNoResults.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getTopFiltersNoResults.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getTopFiltersNoResults.limit - Number of items to return.\n     * @param getTopFiltersNoResults.offset - Position of the first item to return.\n     * @param getTopFiltersNoResults.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTopFiltersNoResults(\n      { index, search, startDate, endDate, limit, offset, tags }: GetTopFiltersNoResultsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetTopFiltersNoResultsResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getTopFiltersNoResults`.');\n      }\n\n      const requestPath = '/2/filters/noResults';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (search !== undefined) {\n        queryParameters['search'] = search.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (limit !== undefined) {\n        queryParameters['limit'] = limit.toString();\n      }\n\n      if (offset !== undefined) {\n        queryParameters['offset'] = offset.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the object IDs of the 1,000 most frequent search results.  If you set the `clickAnalytics` query parameter to true, the response also includes:  - Tracked searches count. Tracked searches are Search API requests with the `clickAnalytics` parameter set to `true`. This differs from the response\\'s `count`, which shows the overall number of searches, including those where `clickAnalytics` is `false`. - Click count - Click-through rate (CTR) - Conversion count - Conversion rate (CR) - Average click position  If you set the `revenueAnalytics` parameter to `true`, the response also includes:  - Add-to-cart count - Add-to-cart rate (ATCR) - Purchase count - Purchase rate - Revenue details for each currency  **There\\'s a difference between 0% rates and null rates:**  - **Null** means there were no queries: since Algolia didn\\'t receive any events, the rates (CTR, CR, ATCR, purchase rate) are null. - **0% rates** mean there _were_ queries but no [click or conversion events](https://www.algolia.com/doc/guides/sending-events/getting-started/) were received.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getTopHits - The getTopHits object.\n     * @param getTopHits.index - Index name.\n     * @param getTopHits.search - Search query.\n     * @param getTopHits.clickAnalytics - Whether to include metrics related to click and conversion events in the response.\n     * @param getTopHits.revenueAnalytics - Whether to include metrics related to revenue events in the response.\n     * @param getTopHits.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getTopHits.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getTopHits.limit - Number of items to return.\n     * @param getTopHits.offset - Position of the first item to return.\n     * @param getTopHits.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTopHits(\n      { index, search, clickAnalytics, revenueAnalytics, startDate, endDate, limit, offset, tags }: GetTopHitsProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetTopHitsResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getTopHits`.');\n      }\n\n      const requestPath = '/2/hits';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (search !== undefined) {\n        queryParameters['search'] = search.toString();\n      }\n\n      if (clickAnalytics !== undefined) {\n        queryParameters['clickAnalytics'] = clickAnalytics.toString();\n      }\n\n      if (revenueAnalytics !== undefined) {\n        queryParameters['revenueAnalytics'] = revenueAnalytics.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (limit !== undefined) {\n        queryParameters['limit'] = limit.toString();\n      }\n\n      if (offset !== undefined) {\n        queryParameters['offset'] = offset.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Returns the most popular searches. For each search, it also includes the average number of hits.  If you set the `clickAnalytics` query parameter to `true`, the response also includes  - Tracked searches count. Tracked searches are Search API requests with the `clickAnalytics` parameter set to `true`. This differs from the response\\'s `count`, which shows the overall number of searches, including those where `clickAnalytics` is `false`. - Click count - Click-through rate (CTR) - Conversion count - Conversion rate (CR) - Average click position  If you set the `revenueAnalytics` query parameter to `true`, the response also includes:  - Add-to-cart count - Add-to-cart rate (ATCR) - Purchase count - Purchase rate - Revenue details for each currency  **There\\'s a difference between 0% rates and null rates:**  - **Null** means there were no queries: since Algolia didn\\'t receive any events, the rates (CTR, CR, ATCR, purchase rate) are null. - **0% rates** mean there _were_ queries but no [click or conversion events](https://www.algolia.com/doc/guides/sending-events/getting-started/) were received.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getTopSearches - The getTopSearches object.\n     * @param getTopSearches.index - Index name.\n     * @param getTopSearches.clickAnalytics - Whether to include metrics related to click and conversion events in the response.\n     * @param getTopSearches.revenueAnalytics - Whether to include metrics related to revenue events in the response.\n     * @param getTopSearches.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getTopSearches.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getTopSearches.orderBy - Attribute by which to order the response items.  If the `clickAnalytics` parameter is false, only `searchCount` is available.\n     * @param getTopSearches.direction - Sorting direction of the results: ascending or descending.\n     * @param getTopSearches.limit - Number of items to return.\n     * @param getTopSearches.offset - Position of the first item to return.\n     * @param getTopSearches.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getTopSearches(\n      {\n        index,\n        clickAnalytics,\n        revenueAnalytics,\n        startDate,\n        endDate,\n        orderBy,\n        direction,\n        limit,\n        offset,\n        tags,\n      }: GetTopSearchesProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetTopSearchesResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getTopSearches`.');\n      }\n\n      const requestPath = '/2/searches';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (clickAnalytics !== undefined) {\n        queryParameters['clickAnalytics'] = clickAnalytics.toString();\n      }\n\n      if (revenueAnalytics !== undefined) {\n        queryParameters['revenueAnalytics'] = revenueAnalytics.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (orderBy !== undefined) {\n        queryParameters['orderBy'] = orderBy.toString();\n      }\n\n      if (direction !== undefined) {\n        queryParameters['direction'] = direction.toString();\n      }\n\n      if (limit !== undefined) {\n        queryParameters['limit'] = limit.toString();\n      }\n\n      if (offset !== undefined) {\n        queryParameters['offset'] = offset.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n\n    /**\n     * Retrieves the number of unique users within a time range, including a daily breakdown.  Since it returns the number of unique users, the sum of the daily values might be different from the total number.  By default:  - Algolia distinguishes search users by their IP address, _unless_ you include a pseudonymous user identifier in your search requests with the `userToken` API parameter or `x-algolia-usertoken` request header. - The analyzed period includes the last eight days including the current day.\n     *\n     * Required API Key ACLs:\n     *  - analytics\n     * @param getUsersCount - The getUsersCount object.\n     * @param getUsersCount.index - Index name.\n     * @param getUsersCount.startDate - Start date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getUsersCount.endDate - End date of the period to analyze, in `YYYY-MM-DD` format.\n     * @param getUsersCount.tags - Tags by which to segment the analytics.  You can combine multiple tags with `OR` and `AND`. Tags must be URL-encoded. For more information, see [Segment your analytics data](https://www.algolia.com/doc/guides/search-analytics/guides/segments/).\n     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.\n     */\n    getUsersCount(\n      { index, startDate, endDate, tags }: GetUsersCountProps,\n      requestOptions?: RequestOptions,\n    ): Promise<GetUsersCountResponse> {\n      if (!index) {\n        throw new Error('Parameter `index` is required when calling `getUsersCount`.');\n      }\n\n      const requestPath = '/2/users/count';\n      const headers: Headers = {};\n      const queryParameters: QueryParameters = {};\n\n      if (index !== undefined) {\n        queryParameters['index'] = index.toString();\n      }\n\n      if (startDate !== undefined) {\n        queryParameters['startDate'] = startDate.toString();\n      }\n\n      if (endDate !== undefined) {\n        queryParameters['endDate'] = endDate.toString();\n      }\n\n      if (tags !== undefined) {\n        queryParameters['tags'] = tags.toString();\n      }\n\n      const request: Request = {\n        method: 'GET',\n        path: requestPath,\n        queryParameters,\n        headers,\n      };\n\n      return transporter.request(request, requestOptions);\n    },\n  };\n}\n"], "mappings": ";AAIA,SAAS,mBAAmB,iBAAiB,wBAAwB;AACrE,SAAS,4BAA4B;;;ACKrC,SAAS,YAAY,mBAAmB,uBAAuB;AAkDxD,IAAM,mBAAmB;AAEzB,IAAM,UAAU,CAAC,MAAM,IAAI;AAIlC,SAAS,gBAAgB,QAAyB;AAChD,QAAM,MAAM,CAAC,SAAS,0BAA0B,iCAAiC,QAAQ,YAAY,MAAM;AAE3G,SAAO,CAAC,EAAE,KAAK,QAAQ,aAAa,UAAU,QAAQ,CAAC;AACzD;AAEO,SAAS,sBAAsB;AAAA,EACpC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR,GAAG;AACL,GAAwC;AACtC,QAAM,OAAO,WAAW,aAAa,cAAc,QAAQ;AAC3D,QAAM,cAAc,kBAAkB;AAAA,IACpC,OAAO,gBAAgB,YAAY;AAAA,IACnC,GAAG;AAAA,IACH,cAAc,gBAAgB;AAAA,MAC5B;AAAA,MACA,QAAQ;AAAA,MACR,SAAS;AAAA,IACX,CAAC;AAAA,IACD,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,GAAG,KAAK,QAAQ;AAAA,MAChB,GAAG,QAAQ;AAAA,IACb;AAAA,IACA,qBAAqB;AAAA,MACnB,GAAG,KAAK,gBAAgB;AAAA,MACxB,GAAG,QAAQ;AAAA,IACb;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACL;AAAA;AAAA;AAAA;AAAA,IAKA,OAAO;AAAA;AAAA;AAAA;AAAA,IAKP,QAAQ;AAAA;AAAA;AAAA;AAAA,IAKR,aAA4B;AAC1B,aAAO,QAAQ,IAAI,CAAC,YAAY,cAAc,MAAM,GAAG,YAAY,eAAe,MAAM,CAAC,CAAC,EAAE,KAAK,MAAM,MAAS;AAAA,IAClH;AAAA;AAAA;AAAA;AAAA,IAKA,IAAI,MAAc;AAChB,aAAO,YAAY,aAAa;AAAA,IAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,gBAAgB,SAAiB,SAAwB;AACvD,kBAAY,aAAa,IAAI,EAAE,SAAS,QAAQ,CAAC;AAAA,IACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,gBAAgB,EAAE,OAAO,GAA6B;AACpD,UAAI,CAAC,YAAY,aAAa,iBAAiB;AAC7C,oBAAY,YAAY,mBAAmB,IAAI;AAAA,MACjD,OAAO;AACL,oBAAY,oBAAoB,mBAAmB,IAAI;AAAA,MACzD;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,aACE,EAAE,MAAM,WAAW,GACnB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,2DAA2D;AAAA,MAC7E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,UAAU,EAAE,MAAM,WAAW,GAAmB,gBAAmE;AACjH,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,WACE,EAAE,MAAM,YAAY,KAAK,GACzB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,OAAO,OAAO,CAAC;AAAA,MACvB;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,UACE,EAAE,MAAM,YAAY,KAAK,GACzB,gBACkC;AAClC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAEA,YAAM,cAAc,UAAU,QAAQ,UAAU,IAAI;AACpD,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,aAAa,aAAa,CAAC;AAEpE,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA,MAAM,OAAO,OAAO,CAAC;AAAA,MACvB;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,iBACE,EAAE,OAAO,WAAW,SAAS,KAAK,GAClC,gBACmC;AACnC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,wBACE,EAAE,OAAO,WAAW,SAAS,KAAK,GAClC,gBAC0C;AAC1C,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,uEAAuE;AAAA,MACzF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,kBACE,EAAE,OAAO,WAAW,SAAS,KAAK,GAClC,gBACoC;AACpC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,iEAAiE;AAAA,MACnF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,oBACE,EAAE,OAAO,WAAW,SAAS,KAAK,GAClC,gBACsC;AACtC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,kBACE,EAAE,OAAO,WAAW,SAAS,KAAK,GAClC,gBACoC;AACpC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,iEAAiE;AAAA,MACnF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,eACE,EAAE,OAAO,WAAW,SAAS,KAAK,GAClC,gBACiC;AACjC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,8DAA8D;AAAA,MAChF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,iBACE,EAAE,OAAO,WAAW,SAAS,KAAK,GAClC,gBACmC;AACnC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,gBACE,EAAE,OAAO,WAAW,SAAS,KAAK,GAClC,gBACkC;AAClC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,WACE,EAAE,OAAO,WAAW,SAAS,KAAK,GAClC,gBACqB;AACrB,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,0DAA0D;AAAA,MAC5E;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,iBACE,EAAE,OAAO,WAAW,SAAS,KAAK,GAClC,gBACmC;AACnC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,gEAAgE;AAAA,MAClF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAgBA,oBACE,EAAE,OAAO,WAAW,SAAS,OAAO,QAAQ,KAAK,GACjD,gBACsC;AACtC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACrF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAgBA,qBACE,EAAE,OAAO,WAAW,SAAS,OAAO,QAAQ,KAAK,GACjD,gBACuC;AACvC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,oEAAoE;AAAA,MACtF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,UAAU,EAAE,MAAM,GAAmB,gBAA6D;AAChG,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,yDAAyD;AAAA,MAC3E;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAgBA,gBACE,EAAE,OAAO,WAAW,SAAS,OAAO,QAAQ,KAAK,GACjD,gBACkC;AAClC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,+DAA+D;AAAA,MACjF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAiBA,uBACE,EAAE,OAAO,QAAQ,WAAW,SAAS,OAAO,QAAQ,KAAK,GACzD,gBACyC;AACzC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,sEAAsE;AAAA,MACxF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAkBA,yBACE,EAAE,WAAW,OAAO,QAAQ,WAAW,SAAS,OAAO,QAAQ,KAAK,GACpE,gBAC2C;AAC3C,UAAI,CAAC,WAAW;AACd,cAAM,IAAI,MAAM,4EAA4E;AAAA,MAC9F;AAEA,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,wEAAwE;AAAA,MAC1F;AAEA,YAAM,cAAc,yBAAyB,QAAQ,eAAe,mBAAmB,SAAS,CAAC;AACjG,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAiBA,uBACE,EAAE,OAAO,QAAQ,WAAW,SAAS,OAAO,QAAQ,KAAK,GACzD,gBACyC;AACzC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,sEAAsE;AAAA,MACxF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAmBA,WACE,EAAE,OAAO,QAAQ,gBAAgB,kBAAkB,WAAW,SAAS,OAAO,QAAQ,KAAK,GAC3F,gBAC6B;AAC7B,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,0DAA0D;AAAA,MAC5E;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,mBAAmB,QAAW;AAChC,wBAAgB,gBAAgB,IAAI,eAAe,SAAS;AAAA,MAC9D;AAEA,UAAI,qBAAqB,QAAW;AAClC,wBAAgB,kBAAkB,IAAI,iBAAiB,SAAS;AAAA,MAClE;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAoBA,eACE;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,GACA,gBACiC;AACjC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,8DAA8D;AAAA,MAChF;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,mBAAmB,QAAW;AAChC,wBAAgB,gBAAgB,IAAI,eAAe,SAAS;AAAA,MAC9D;AAEA,UAAI,qBAAqB,QAAW;AAClC,wBAAgB,kBAAkB,IAAI,iBAAiB,SAAS;AAAA,MAClE;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,WAAW,QAAW;AACxB,wBAAgB,QAAQ,IAAI,OAAO,SAAS;AAAA,MAC9C;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,cACE,EAAE,OAAO,WAAW,SAAS,KAAK,GAClC,gBACgC;AAChC,UAAI,CAAC,OAAO;AACV,cAAM,IAAI,MAAM,6DAA6D;AAAA,MAC/E;AAEA,YAAM,cAAc;AACpB,YAAM,UAAmB,CAAC;AAC1B,YAAM,kBAAmC,CAAC;AAE1C,UAAI,UAAU,QAAW;AACvB,wBAAgB,OAAO,IAAI,MAAM,SAAS;AAAA,MAC5C;AAEA,UAAI,cAAc,QAAW;AAC3B,wBAAgB,WAAW,IAAI,UAAU,SAAS;AAAA,MACpD;AAEA,UAAI,YAAY,QAAW;AACzB,wBAAgB,SAAS,IAAI,QAAQ,SAAS;AAAA,MAChD;AAEA,UAAI,SAAS,QAAW;AACtB,wBAAgB,MAAM,IAAI,KAAK,SAAS;AAAA,MAC1C;AAEA,YAAM,UAAmB;AAAA,QACvB,QAAQ;AAAA,QACR,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAEA,aAAO,YAAY,QAAQ,SAAS,cAAc;AAAA,IACpD;AAAA,EACF;AACF;;;ADh2CO,SAAS,gBACd,OACA,QACA,QACA,SACiB;AACjB,MAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,UAAM,IAAI,MAAM,qBAAqB;AAAA,EACvC;AAEA,MAAI,CAAC,UAAU,OAAO,WAAW,UAAU;AACzC,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC;AAEA,MAAI,WAAW,OAAO,WAAW,YAAY,CAAC,QAAQ,SAAS,MAAM,IAAI;AACvE,UAAM,IAAI,MAAM,4CAA4C,QAAQ,KAAK,IAAI,CAAC,EAAE;AAAA,EAClF;AAEA,SAAO;AAAA,IACL,GAAG,sBAAsB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,QACR,SAAS;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA,QAAQ,iBAAiB;AAAA,MACzB,WAAW,qBAAqB;AAAA,MAChC,eAAe,CAAC,EAAE,SAAS,SAAS,CAAC;AAAA,MACrC,gBAAgB,gBAAgB;AAAA,MAChC,eAAe,gBAAgB;AAAA,MAC/B,YAAY,kBAAkB;AAAA,MAC9B,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AACF;", "names": []}