import * as _algolia_client_common from '@algolia/client-common';
import { CreateClientOptions, RequestOptions, ClientOptions } from '@algolia/client-common';

/**
 * Configuration for handling empty searches.
 */
type EmptySearch = {
    /**
     * Whether to exclude empty searches when calculating A/B test results.
     */
    exclude?: boolean;
};

/**
 * Metric for which you want to detect the smallest relative difference.
 */
type EffectMetric = 'addToCartRate' | 'clickThroughRate' | 'conversionRate' | 'purchaseRate';

/**
 * Configuration for the smallest difference between test variants you want to detect.
 */
type MinimumDetectableEffect = {
    /**
     * Smallest difference in an observable metric between variants. For example, to detect a 10% difference between variants, set this value to 0.1.
     */
    size: number;
    metric: EffectMetric;
};

/**
 * Configuration for handling outliers.
 */
type Outliers = {
    /**
     * Whether to exclude outliers when calculating A/B test results.
     */
    exclude?: boolean;
};

/**
 * A/B test configuration.
 */
type ABTestConfiguration = {
    outliers?: Outliers;
    emptySearch?: EmptySearch;
    minimumDetectableEffect?: MinimumDetectableEffect;
};

/**
 * A/B test status.  - `active`. The A/B test is live and search traffic is split between the two variants. - `stopped`. You stopped the A/B test. The A/B test data is still available for analysis. - `expired`. The A/B test was automatically stopped after reaching its end date. - `failed`. Creating the A/B test failed.
 */
type Status = 'active' | 'stopped' | 'expired' | 'failed';

type Currency = {
    /**
     * Currency code.
     */
    currency?: string;
    /**
     * Revenue for this currency.
     */
    revenue?: number;
    /**
     * Mean for this currency.
     */
    mean?: number;
    /**
     * Standard deviation for this currency.
     */
    standardDeviation?: number;
};

/**
 * Empty searches removed from the A/B test as a result of configuration settings.
 */
type EmptySearchFilter = {
    /**
     * Number of users removed from the A/B test.
     */
    usersCount?: number;
    /**
     * Number of tracked searches removed from the A/B test.
     */
    trackedSearchesCount?: number;
};

/**
 * Outliers removed from the A/B test as a result of configuration settings.
 */
type OutliersFilter = {
    /**
     * Number of users removed from the A/B test.
     */
    usersCount?: number;
    /**
     * Number of tracked searches removed from the A/B test.
     */
    trackedSearchesCount?: number;
};

/**
 * A/B test filter effects resulting from configuration settings.
 */
type FilterEffects = {
    outliers?: OutliersFilter;
    emptySearch?: EmptySearchFilter;
};

type Variant = {
    /**
     * Number of add-to-cart events for this variant.
     */
    addToCartCount: number;
    /**
     * [Add-to-cart rate](https://www.algolia.com/doc/guides/search-analytics/concepts/metrics/#add-to-cart-rate) for this variant.
     */
    addToCartRate?: number | null;
    /**
     * [Average click position](https://www.algolia.com/doc/guides/search-analytics/concepts/metrics/#click-position) for this variant.
     */
    averageClickPosition?: number | null;
    /**
     * Number of click events for this variant.
     */
    clickCount: number;
    /**
     * [Click-through rate](https://www.algolia.com/doc/guides/search-analytics/concepts/metrics/#click-through-rate) for this variant.
     */
    clickThroughRate?: number | null;
    /**
     * Number of click events for this variant.
     */
    conversionCount: number;
    /**
     * [Conversion rate](https://www.algolia.com/doc/guides/search-analytics/concepts/metrics/#conversion-rate) for this variant.
     */
    conversionRate?: number | null;
    /**
     * A/B test currencies.
     */
    currencies?: {
        [key: string]: Currency;
    };
    /**
     * Description for this variant.
     */
    description?: string;
    /**
     * Estimated number of searches required to achieve the desired statistical significance.  The A/B test configuration must include a `mininmumDetectableEffect` setting for this number to be included in the response.
     */
    estimatedSampleSize?: number;
    filterEffects?: FilterEffects;
    /**
     * Index name of the A/B test variant (case-sensitive).
     */
    index: string;
    /**
     * Number of [searches without results](https://www.algolia.com/doc/guides/search-analytics/concepts/metrics/#searches-without-results) for this variant.
     */
    noResultCount: number | null;
    /**
     * Number of purchase events for this variant.
     */
    purchaseCount: number;
    /**
     * [Purchase rate](https://www.algolia.com/doc/guides/search-analytics/concepts/metrics/#purchase-rate) for this variant.
     */
    purchaseRate?: number | null;
    /**
     * Number of searches for this variant.
     */
    searchCount: number | null;
    /**
     * Number of tracked searches. Tracked searches are search requests where the `clickAnalytics` parameter is true.
     */
    trackedSearchCount?: number;
    /**
     * Percentage of search requests each variant receives.
     */
    trafficPercentage: number;
    /**
     * Number of users that made searches to this variant.
     */
    userCount: number | null;
    /**
     * Number of users that made tracked searches to this variant.
     */
    trackedUserCount: number | null;
};

type ABTest = {
    /**
     * Unique A/B test identifier.
     */
    abTestID: number;
    clickSignificance?: number | null;
    conversionSignificance?: number | null;
    addToCartSignificance?: number | null;
    purchaseSignificance?: number | null;
    revenueSignificance?: {
        [key: string]: number;
    } | null;
    /**
     * Date and time when the A/B test was last updated, in RFC 3339 format.
     */
    updatedAt: string;
    /**
     * Date and time when the A/B test was created, in RFC 3339 format.
     */
    createdAt: string;
    /**
     * End date and time of the A/B test, in RFC 3339 format.
     */
    endAt: string;
    /**
     * A/B test name.
     */
    name: string;
    status: Status;
    /**
     * A/B test variants.  The first variant is your _control_ index, typically your production index. The second variant is an index with changed settings that you want to test against the control.
     */
    variants: Array<Variant>;
    configuration?: ABTestConfiguration;
};

type ABTestResponse = {
    /**
     * Index name of the A/B test variant (case-sensitive).
     */
    index: string;
    /**
     * Unique A/B test identifier.
     */
    abTestID: number;
    /**
     * Unique identifier of a task.  A successful API response means that a task was added to a queue. It might not run immediately. You can check the task\'s progress with the [`task` operation](#tag/Indices/operation/getTask) and this `taskID`.
     */
    taskID: number;
};

type AbTestsVariant = {
    /**
     * Index name of the A/B test variant (case-sensitive).
     */
    index: string;
    /**
     * Percentage of search requests each variant receives.
     */
    trafficPercentage: number;
    /**
     * Description for this variant.
     */
    description?: string;
};

/**
 * Search parameters to add to the test variant. Only use this parameter if the two variants use the same index.
 */
type CustomSearchParams = {
    customSearchParameters: Record<string, unknown>;
};

type AbTestsVariantSearchParams = AbTestsVariant & CustomSearchParams;

type AddABTestsVariant = AbTestsVariant | AbTestsVariantSearchParams;

type AddABTestsRequest = {
    /**
     * A/B test name.
     */
    name: string;
    /**
     * A/B test variants.
     */
    variants: Array<AddABTestsVariant>;
    /**
     * End date and time of the A/B test, in RFC 3339 format.
     */
    endAt: string;
};

/**
 * A/B test configuration for estimating the sample size and duration using minimum detectable effect.
 */
type EstimateConfiguration = {
    outliers?: Outliers;
    emptySearch?: EmptySearch;
    minimumDetectableEffect: MinimumDetectableEffect;
};

type EstimateABTestRequest = {
    configuration: EstimateConfiguration;
    /**
     * A/B test variants.
     */
    variants: Array<AddABTestsVariant>;
};

type EstimateABTestResponse = {
    /**
     * Estimated number of days needed to reach the sample sizes required for detecting the configured effect. This value is based on historical traffic.
     */
    durationDays?: number;
    /**
     * Sample size estimates for each variant. The first element is the control variant. Each element is the estimated number of searches required to achieve the desired statistical significance.
     */
    sampleSizes?: Array<number>;
};

type ListABTestsResponse = {
    /**
     * A/B tests.
     */
    abtests: Array<ABTest> | null;
    /**
     * Number of A/B tests.
     */
    count: number;
    /**
     * Number of retrievable A/B tests.
     */
    total: number;
};

type ScheduleABTestResponse = {
    /**
     * Unique scheduled A/B test identifier.
     */
    abTestScheduleID: number;
};

type ScheduleABTestsRequest = {
    /**
     * A/B test name.
     */
    name: string;
    /**
     * A/B test variants.
     */
    variants: Array<AddABTestsVariant>;
    /**
     * Date and time when the A/B test is scheduled to start, in RFC 3339 format.
     */
    scheduledAt: string;
    /**
     * End date and time of the A/B test, in RFC 3339 format.
     */
    endAt: string;
};

/**
 * Properties for the `customDelete` method.
 */
type CustomDeleteProps = {
    /**
     * Path of the endpoint, anything after \"/1\" must be specified.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    };
};
/**
 * Properties for the `customGet` method.
 */
type CustomGetProps = {
    /**
     * Path of the endpoint, anything after \"/1\" must be specified.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    };
};
/**
 * Properties for the `customPost` method.
 */
type CustomPostProps = {
    /**
     * Path of the endpoint, anything after \"/1\" must be specified.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    };
    /**
     * Parameters to send with the custom request.
     */
    body?: Record<string, unknown>;
};
/**
 * Properties for the `customPut` method.
 */
type CustomPutProps = {
    /**
     * Path of the endpoint, anything after \"/1\" must be specified.
     */
    path: string;
    /**
     * Query parameters to apply to the current query.
     */
    parameters?: {
        [key: string]: any;
    };
    /**
     * Parameters to send with the custom request.
     */
    body?: Record<string, unknown>;
};
/**
 * Properties for the `deleteABTest` method.
 */
type DeleteABTestProps = {
    /**
     * Unique A/B test identifier.
     */
    id: number;
};
/**
 * Properties for the `getABTest` method.
 */
type GetABTestProps = {
    /**
     * Unique A/B test identifier.
     */
    id: number;
};
/**
 * Properties for the `listABTests` method.
 */
type ListABTestsProps = {
    /**
     * Position of the first item to return.
     */
    offset?: number;
    /**
     * Number of items to return.
     */
    limit?: number;
    /**
     * Index name prefix. Only A/B tests for indices starting with this string are included in the response.
     */
    indexPrefix?: string;
    /**
     * Index name suffix. Only A/B tests for indices ending with this string are included in the response.
     */
    indexSuffix?: string;
};
/**
 * Properties for the `stopABTest` method.
 */
type StopABTestProps = {
    /**
     * Unique A/B test identifier.
     */
    id: number;
};

declare const apiClientVersion = "5.20.0";
declare const REGIONS: readonly ["de", "us"];
type Region = (typeof REGIONS)[number];
type RegionOptions = {
    region?: Region;
};
declare function createAbtestingClient({ appId: appIdOption, apiKey: apiKeyOption, authMode, algoliaAgents, region: regionOption, ...options }: CreateClientOptions & RegionOptions): {
    transporter: _algolia_client_common.Transporter;
    /**
     * The `appId` currently in use.
     */
    appId: string;
    /**
     * The `apiKey` currently in use.
     */
    apiKey: string;
    /**
     * Clears the cache of the transporter for the `requestsCache` and `responsesCache` properties.
     */
    clearCache(): Promise<void>;
    /**
     * Get the value of the `algoliaAgent`, used by our libraries internally and telemetry system.
     */
    readonly _ua: string;
    /**
     * Adds a `segment` to the `x-algolia-agent` sent with every requests.
     *
     * @param segment - The algolia agent (user-agent) segment to add.
     * @param version - The version of the agent.
     */
    addAlgoliaAgent(segment: string, version?: string): void;
    /**
     * Helper method to switch the API key used to authenticate the requests.
     *
     * @param params - Method params.
     * @param params.apiKey - The new API Key to use.
     */
    setClientApiKey({ apiKey }: {
        apiKey: string;
    }): void;
    /**
     * Creates a new A/B test.
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param addABTestsRequest - The addABTestsRequest object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    addABTests(addABTestsRequest: AddABTestsRequest, requestOptions?: RequestOptions): Promise<ABTestResponse>;
    /**
     * This method allow you to send requests to the Algolia REST API.
     * @param customDelete - The customDelete object.
     * @param customDelete.path - Path of the endpoint, anything after \"/1\" must be specified.
     * @param customDelete.parameters - Query parameters to apply to the current query.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customDelete({ path, parameters }: CustomDeleteProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method allow you to send requests to the Algolia REST API.
     * @param customGet - The customGet object.
     * @param customGet.path - Path of the endpoint, anything after \"/1\" must be specified.
     * @param customGet.parameters - Query parameters to apply to the current query.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customGet({ path, parameters }: CustomGetProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method allow you to send requests to the Algolia REST API.
     * @param customPost - The customPost object.
     * @param customPost.path - Path of the endpoint, anything after \"/1\" must be specified.
     * @param customPost.parameters - Query parameters to apply to the current query.
     * @param customPost.body - Parameters to send with the custom request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customPost({ path, parameters, body }: CustomPostProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * This method allow you to send requests to the Algolia REST API.
     * @param customPut - The customPut object.
     * @param customPut.path - Path of the endpoint, anything after \"/1\" must be specified.
     * @param customPut.parameters - Query parameters to apply to the current query.
     * @param customPut.body - Parameters to send with the custom request.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    customPut({ path, parameters, body }: CustomPutProps, requestOptions?: RequestOptions): Promise<Record<string, unknown>>;
    /**
     * Deletes an A/B test by its ID.
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param deleteABTest - The deleteABTest object.
     * @param deleteABTest.id - Unique A/B test identifier.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    deleteABTest({ id }: DeleteABTestProps, requestOptions?: RequestOptions): Promise<ABTestResponse>;
    /**
     * Given the traffic percentage and the expected effect size, this endpoint estimates the sample size and duration of an A/B test based on historical traffic.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param estimateABTestRequest - The estimateABTestRequest object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    estimateABTest(estimateABTestRequest: EstimateABTestRequest, requestOptions?: RequestOptions): Promise<EstimateABTestResponse>;
    /**
     * Retrieves the details for an A/B test by its ID.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param getABTest - The getABTest object.
     * @param getABTest.id - Unique A/B test identifier.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    getABTest({ id }: GetABTestProps, requestOptions?: RequestOptions): Promise<ABTest>;
    /**
     * Lists all A/B tests you configured for this application.
     *
     * Required API Key ACLs:
     *  - analytics
     * @param listABTests - The listABTests object.
     * @param listABTests.offset - Position of the first item to return.
     * @param listABTests.limit - Number of items to return.
     * @param listABTests.indexPrefix - Index name prefix. Only A/B tests for indices starting with this string are included in the response.
     * @param listABTests.indexSuffix - Index name suffix. Only A/B tests for indices ending with this string are included in the response.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    listABTests({ offset, limit, indexPrefix, indexSuffix }?: ListABTestsProps, requestOptions?: RequestOptions | undefined): Promise<ListABTestsResponse>;
    /**
     * Schedule an A/B test to be started at a later time.
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param scheduleABTestsRequest - The scheduleABTestsRequest object.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    scheduleABTest(scheduleABTestsRequest: ScheduleABTestsRequest, requestOptions?: RequestOptions): Promise<ScheduleABTestResponse>;
    /**
     * Stops an A/B test by its ID.  You can\'t restart stopped A/B tests.
     *
     * Required API Key ACLs:
     *  - editSettings
     * @param stopABTest - The stopABTest object.
     * @param stopABTest.id - Unique A/B test identifier.
     * @param requestOptions - The requestOptions to send along with the query, they will be merged with the transporter requestOptions.
     */
    stopABTest({ id }: StopABTestProps, requestOptions?: RequestOptions): Promise<ABTestResponse>;
};

/**
 * Error.
 */
type ErrorBase = Record<string, any> & {
    message?: string;
};

type AbtestingClient = ReturnType<typeof createAbtestingClient>;

declare function abtestingClient(appId: string, apiKey: string, region?: Region, options?: ClientOptions): AbtestingClient;

export { type ABTest, type ABTestConfiguration, type ABTestResponse, type AbTestsVariant, type AbTestsVariantSearchParams, type AbtestingClient, type AddABTestsRequest, type AddABTestsVariant, type Currency, type CustomDeleteProps, type CustomGetProps, type CustomPostProps, type CustomPutProps, type CustomSearchParams, type DeleteABTestProps, type EffectMetric, type EmptySearch, type EmptySearchFilter, type ErrorBase, type EstimateABTestRequest, type EstimateABTestResponse, type EstimateConfiguration, type FilterEffects, type GetABTestProps, type ListABTestsProps, type ListABTestsResponse, type MinimumDetectableEffect, type Outliers, type OutliersFilter, type Region, type RegionOptions, type ScheduleABTestResponse, type ScheduleABTestsRequest, type Status, type StopABTestProps, type Variant, abtestingClient, apiClientVersion };
