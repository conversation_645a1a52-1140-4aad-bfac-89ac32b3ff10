/**
 * Email template generator for SES
 * Replaces SendGrid dynamic templates with HTML template generation
 */

/**
 * Generate contact form email template
 * @param {Object} formData - Form submission data
 * @returns {Object} - Email subject and HTML body
 */
export const generateContactFormEmail = (formData) => {
  const subject = `New Contact Form Submission from ${formData.firstName} ${formData.lastName}`;

  const htmlBody = `
<!DOCTYPE html>
<html>
<head>
    <title>Lead Details</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f9f9f9; padding: 20px; margin: 0;">
    <table style="max-width: 600px; width: 100%; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); margin: auto;">
        <tr>
            <td style="text-align: center; padding-bottom: 20px;">
                <div style="background-color: black; padding: 20px; text-align: center;">
                    <img src="https://cdn.marutitech.com/maruti_logo_5897473ce8.png" 
                         alt="Maruti Tech Logo" 
                         style="max-width: 150px;">
                </div> 
            </td>
        </tr>
        <tr>
            <td>
                <h2 style="text-align: center; color: #333;">Form Data</h2>
                <hr style="border: 0; height: 1px; background-color: #ddd; margin: 10px 0;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr><td style="padding: 8px;"><b>First Name:</b></td><td>${lead.firstName}</td></tr>
                    <tr><td style="padding: 8px;"><b>Last Name:</b></td><td>${lead.lastName}</td></tr>
                    <tr><td style="padding: 8px;"><b>Email Address:</b></td><td>${lead.emailAddress}</td></tr>
                    <tr><td style="padding: 8px;"><b>Phone Number:</b></td><td>${lead.phoneNumber}</td></tr>
                    <tr><td style="padding: 8px;"><b>Company Name:</b></td><td>${lead.companyName}</td></tr>
                    <tr><td style="padding: 8px;"><b>How Did You Hear About Us?:</b></td><td>${lead.howDidYouHearAboutUs}</td></tr>
                    <tr><td style="padding: 8px;"><b>How Can We Help You?:</b></td><td>${lead.howCanWeHelpYou}</td></tr>
                    <tr><td style="padding: 8px;"><b>UTM Campaign:</b></td><td>${lead.utm_campaign}</td></tr>
                    <tr><td style="padding: 8px;"><b>UTM Medium:</b></td><td>${lead.utm_medium}</td></tr>
                    <tr><td style="padding: 8px;"><b>UTM Source:</b></td><td>${lead.utm_source}</td></tr>
                    <tr><td style="padding: 8px;"><b>IP Address:</b></td><td>${lead.ip_address}</td></tr>
                    <tr><td style="padding: 8px;"><b>GA 4 User ID:</b></td><td>${lead.ga_4_userid}</td></tr>
                    <tr><td style="padding: 8px;"><b>City:</b></td><td>${lead.city}</td></tr>
                    <tr><td style="padding: 8px;"><b>Country:</b></td><td>${lead.country}</td></tr>
                    <tr><td style="padding: 8px;"><b>Secondary Source:</b></td><td>${lead.secondary_source}</td></tr>
                    <tr><td style="padding: 8px;"><b>Clarity:</b></td><td>${lead.clarity}</td></tr>
                    <tr><td style="padding: 8px;"><b>URL:</b></td><td>${lead.url}</td></tr>
                    <tr><td style="padding: 8px;"><b>Referrer:</b></td><td>${lead.referrer}</td></tr>
                    <tr><td style="padding: 8px;"><b>Consent:</b></td><td>${lead.consent}</td></tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>

  `;

  const textBody = `
New Contact Form Submission

Name: ${formData.firstName} ${formData.lastName}
Email: ${formData.emailAddress}
Phone: ${formData.phoneNumber || "Not provided"}
Company: ${formData.companyName || "Not provided"}
Message: ${formData.message || "No message provided"}
Source: ${formData.secondary_source || "Unknown"}
Submission Time: ${new Date().toLocaleString()}

This email was generated automatically from your website contact form.
  `;

  return { subject, htmlBody, textBody };
};

/**
 * Generate AI readiness form email template
 * @param {Object} formData - Form submission data
 * @returns {Object} - Email subject and HTML body
 */
export const generateAIReadinessEmail = (formData) => {
  const subject = `AI Readiness Assessment from ${formData.firstName} ${formData.lastName}`;

  const htmlBody = `
<!DOCTYPE html>
<html>
<head>
    <title>Lead Details</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f9f9f9; padding: 20px; margin: 0;">
    <table style="max-width: 600px; width: 100%; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); margin: auto;">
        <tr>
            <td style="text-align: center; padding-bottom: 20px;">
                <div style="background-color: black; padding: 20px; text-align: center;">
                    <img src="https://cdn.marutitech.com/maruti_logo_5897473ce8.png" 
                         alt="Maruti Tech Logo" 
                         style="max-width: 150px;">
                </div> 
            </td>
        </tr>
        <tr>
            <td>
                <h2 style="text-align: center; color: #333;">Form Data</h2>
                <hr style="border: 0; height: 1px; background-color: #ddd; margin: 10px 0;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr><td><b>First Name:</b></td><td>${lead.firstName}</td></tr>
                    <tr><td><b>Last Name:</b></td><td>${lead.lastName}</td></tr>
                    <tr><td><b>Email Address:</b></td><td>${lead.emailAddress}</td></tr>
                    <tr><td><b>Phone Number:</b></td><td>${lead.phoneNumber}</td></tr>
                    <tr><td><b>Company Name:</b></td><td>${lead.companyName}</td></tr>
                    <tr><td><b>UTM Campaign:</b></td><td>${lead.utm_campaign}</td></tr>
                    <tr><td><b>UTM Medium:</b></td><td>${lead.utm_medium}</td></tr>
                    <tr><td><b>UTM Source:</b></td><td>${lead.utm_source}</td></tr>
                    <tr><td><b>IP Address:</b></td><td>${lead.ip_address}</td></tr>
                    <tr><td><b>GA 4 User ID:</b></td><td>${lead.ga_4_userid}</td></tr>
                    <tr><td><b>City:</b></td><td>${lead.city}</td></tr>
                    <tr><td><b>Country:</b></td><td>${lead.country}</td></tr>
                    <tr><td><b>Secondary Source:</b></td><td>${lead.secondary_source}</td></tr>
                    <tr><td><b>Clarity:</b></td><td>${lead.clarity}</td></tr>
                    <tr><td><b>URL:</b></td><td>${lead.url}</td></tr>
                    <tr><td><b>Referrer:</b></td><td>${lead.referrer}</td></tr>
                    <tr><td><b>Consent:</b></td><td>${lead.consent}</td></tr>

                    <tr><td colspan="2"><hr /></td></tr>
                <tr><td colspan="2"><b>AI Project Readiness Questions:</b></td></tr>

                    <tr><td>1. Do you have clearly defined business objectives and goals for the AI project? :</td><td>${lead.do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_}</td></tr>
                    <tr><td colspan="2"><br></td></tr>
                     
                    <tr><td>2. How receptive is your Leadership Team to embracing the changes brought about by AI? :</td><td>${lead.how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_}</td></tr>
                    <tr><td colspan="2"><br></td></tr>
                    
                    <tr><td>3. Do you have budget allocated for your AI project? :</td><td>${lead.do_you_have_budget_allocated_for_your_ai_project_}</td></tr>
                    <tr><td colspan="2"><br></td></tr>
                    
                    <tr><td>4. Do you have a robust data infrastructure for storage, retrieval, and processing? :</td><td>${lead.do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_}</td></tr>
                    <tr><td colspan="2"><br></td></tr>
                    
                    <tr><td>5. Which of the below DB tools do you currently use? :</td><td>${lead.which_of_the_below_db_tools_do_you_currently_use_}</td></tr>
                    <tr><td colspan="2"><br></td></tr>
                    
                    <tr><td>6. Is the relevant data for the AI project available and accessible? :</td><td>${lead.is_the_relevant_data_for_the_ai_project_available_and_accessible_}</td></tr>
                    <tr><td colspan="2"><br></td></tr>
                    
                    <tr><td>7. Do you have access to necessary computing resources (CPU, GPU, cloud services)? :</td><td>${lead.do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__}</td></tr>
                    <tr><td colspan="2"><br></td></tr>
                    
                    <tr><td>8. How would you rate your organization's current IT infrastructure in terms of scalability and flexibility to accommodate the evolving computational needs of AI projects? :</td><td>${lead.how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib}</td></tr>
                    <tr><td colspan="2"><br></td></tr>
                    
                    <tr><td>9. Does the team have the expertise in data science, machine learning, and AI? :</td><td>${lead.does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_}</td></tr>
                    <tr><td colspan="2"><br></td></tr>
                    
                    <tr><td>10. Do you have systems in place to monitor the performance and accuracy of AI models post-deployment? :</td><td>${lead.do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_}</td></tr>
                    <tr><td colspan="2"><br></td></tr>
                    
                    <tr><td>11. Do you have risk management strategies in place for the AI project? :</td><td>${lead.do_you_have_risk_management_strategies_in_place_for_the_ai_project_}</td></tr>
                    <tr><td colspan="2"><br></td></tr>
                    
                    <tr><td>12. Do you have a process in place to measure the impact of the deployment of AI / AI-powered solutions? :</td><td>${lead.do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td colspan="2"><hr /></td></tr>
                    <tr><td><b>Strategy & Leadership :</b></td><td>${lead.strategy___leadership}</td></tr>
                    <tr><td><b>Data Readiness & Infrastructure :</b></td><td>${lead.data_readiness___infrastructure}</td></tr>
                    <tr><td><b>Talent & Skills :</b></td><td>${lead.talent___skills}</td></tr>
                    <tr><td><b>Execution & Monitoring :</b></td><td>${lead.execution___monitoring}</td></tr>
                    <tr><td><b>Impact Evaluation :</b></td><td>${lead.impact_evaliation}</td></tr>
                    <tr><td><b>Average Score :</b></td><td>${lead.average_of_all_score}</td></tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
`;

  const textBody = `
AI Readiness Assessment Submission

Name: ${formData.firstName} ${formData.lastName}
Email: ${formData.emailAddress}
Phone: ${formData.phoneNumber || "Not provided"}
Company: ${formData.companyName || "Not provided"}
${formData.aiReadinessScore ? `AI Readiness Score: ${formData.aiReadinessScore}` : ""}
Source: ${formData.secondary_source || "Unknown"}
Submission Time: ${new Date().toLocaleString()}

This email was generated automatically from your AI readiness assessment form.
  `;

  return { subject, htmlBody, textBody };
};

/**
 * Generate cloud migration form email template
 * @param {Object} formData - Form submission data
 * @returns {Object} - Email subject and HTML body
 */
export const generateCloudMigrationEmail = (formData) => {
  const subject = `Cloud Migration Cost Calculator from ${formData.firstName} ${formData.lastName}`;

  const htmlBody = `
<!DOCTYPE html>
<html>
<head>
    <title>Lead Details</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f9f9f9; padding: 20px; margin: 0;">
    <table style="max-width: 600px; width: 100%; background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); margin: auto;">
        <tr>
            <td style="text-align: center; padding-bottom: 20px;">
                <div style="background-color: black; padding: 20px; text-align: center;">
                    <img src="https://cdn.marutitech.com/maruti_logo_5897473ce8.png"
                         alt="Maruti Tech Logo"
                         style="max-width: 150px;">
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <h2 style="text-align: center; color: #333;">Form Data</h2>
                <hr style="border: 0; height: 1px; background-color: #ddd; margin: 10px 0;">
                <table style="width: 100%; border-collapse: collapse;">
                    <tr><td><b>First Name:</b></td><td>${formData.firstName || "Not provided"}</td></tr>
                    <tr><td><b>Last Name:</b></td><td>${formData.lastName || "Not provided"}</td></tr>
                    <tr><td><b>Email Address:</b></td><td>${formData.emailAddress || "Not provided"}</td></tr>
                    <tr><td><b>Phone Number:</b></td><td>${formData.phoneNumber || "Not provided"}</td></tr>
                    <tr><td><b>Company Name:</b></td><td>${formData.companyName || "Not provided"}</td></tr>
                    <tr><td><b>How Can We Help You:</b></td><td>${formData.howCanWeHelpYou || "Not provided"}</td></tr>
                    <tr><td><b>UTM Campaign:</b></td><td>${formData.utm_campaign || "Not provided"}</td></tr>
                    <tr><td><b>UTM Medium:</b></td><td>${formData.utm_medium || "Not provided"}</td></tr>
                    <tr><td><b>UTM Source:</b></td><td>${formData.utm_source || "Not provided"}</td></tr>
                    <tr><td><b>IP Address:</b></td><td>${formData.ip_address || "Not provided"}</td></tr>
                    <tr><td><b>GA 4 User ID:</b></td><td>${formData.ga_4_userid || "Not provided"}</td></tr>
                    <tr><td><b>City:</b></td><td>${formData.city || "Not provided"}</td></tr>
                    <tr><td><b>Country:</b></td><td>${formData.country || "Not provided"}</td></tr>
                    <tr><td><b>Secondary Source:</b></td><td>${formData.secondary_source || "Not provided"}</td></tr>
                    <tr><td><b>Clarity:</b></td><td>${formData.clarity || "Not provided"}</td></tr>
                    <tr><td><b>URL:</b></td><td>${formData.url || "Not provided"}</td></tr>
                    <tr><td><b>Referrer:</b></td><td>${formData.referrer || "Not provided"}</td></tr>
                    <tr><td><b>Consent:</b></td><td>${formData.consent || "Not provided"}</td></tr>

                    <tr><td colspan="2"><hr /></td></tr>
                    <tr><td colspan="2"><b>Cloud Migration Cost Calculator Results:</b></td></tr>

                    <tr><td><b>Lower Range:</b></td><td>$${formData.lowerRange || "Not calculated"}</td></tr>
                    <tr><td><b>Upper Range:</b></td><td>$${formData.upperRange || "Not calculated"}</td></tr>
                    <tr><td><b>Total Cost:</b></td><td>$${formData.totalCost || "Not calculated"}</td></tr>
                    <tr><td><b>Minimum Cost:</b></td><td>$${formData.minimum_cost || "Not calculated"}</td></tr>
                    <tr><td><b>Maximum Migration Cost:</b></td><td>$${formData.maximum_migration_cost || "Not calculated"}</td></tr>

                    <tr><td colspan="2"><hr /></td></tr>
                    <tr><td colspan="2"><b>Section 1: Business & Infrastructure Assessment</b></td></tr>

                    <tr><td>1. Which elements are you planning to migrate to the cloud?:</td><td>${formData.which_elements_are_you_planning_to_migrate_to_the_cloud || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td>2. Approximately how many servers do you intend to migrate?:</td><td>${formData.approximately_how_many_servers_do_you_intend_to_migrate || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td>3. What is the type of data migration you intend to do?:</td><td>${formData.what_is_the_type_of_data_migration_you_intend_to_do || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td>4. What is your current IT infrastructure setup?:</td><td>${formData.what_is_your_current_it_infrastructure_setup || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td>5. What is the total capacity of your servers?:</td><td>${formData.what_is_the_total_capacity_of_your_servers || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td>6. What is the current monthly infrastructure cost of your current setup?:</td><td>${formData.what_is_the_current_monthly_infrastructure_cost_of_your_current_setup || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td>7. What is the main purpose behind your decision to migrate to the cloud?:</td><td>${formData.what_is_the_main_purpose_behind_your_decision_to_migrate_to_the_cloud || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td colspan="2"><hr /></td></tr>
                    <tr><td colspan="2"><b>Section 2: Workload & Resource Analysis</b></td></tr>

                    <tr><td>8. What type of workloads do you run?:</td><td>${formData.what_type_of_workloads_do_you_run || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td>9. What is the average CPU and memory usage of your workloads?:</td><td>${formData.what_is_the_average_cpu_and_memory_usage_of_your_workloads || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td>10. Do you require high availability or disaster recovery for critical applications?:</td><td>${formData.do_you_require_high_availability_or_disaster_recovery_for_critical_applications || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td colspan="2"><hr /></td></tr>
                    <tr><td colspan="2"><b>Section 3: Cloud Provider & Deployment Preferences</b></td></tr>

                    <tr><td>11. Which cloud provider(s) are you considering?:</td><td>${formData.which_cloud_provider_s_are_you_considering || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td>12. Do you plan to use reserved instances, spot instances, or pay-as-you-go pricing models?:</td><td>${formData.do_you_plan_to_use_reserved_instances_spot_instances_or_pay_as_you_go_pricing_models || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td>13. Which cloud environments are you planning to deploy?:</td><td>${formData.which_cloud_environments_are_you_planning_to_deploy || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td colspan="2"><hr /></td></tr>
                    <tr><td colspan="2"><b>Section 4: Security, Compliance & Migration Strategy</b></td></tr>

                    <tr><td>14. Do you have any specific compliance or regulatory requirements that your cloud migration needs to meet?:</td><td>${formData.do_you_have_any_specific_compliance_or_regulatory_requirements_that_your_cloud_migration_needs_to_meet || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td>15. What migration strategy do you prefer?:</td><td>${formData.what_migration_strategy_do_you_prefer || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td colspan="2"><hr /></td></tr>
                    <tr><td colspan="2"><b>Section 5: Post-Migration & Optimization</b></td></tr>

                    <tr><td>16. Do you need auto-scaling capabilities for cost optimization?:</td><td>${formData.do_you_need_auto_scaling_capabilities_for_cost_optimization || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td>17. How often do you plan to review and optimize your cloud expenses?:</td><td>${formData.how_often_do_you_plan_to_review_and_optimize_your_cloud_expenses || "Not provided"}</td></tr>
                    <tr><td colspan="2"><br></td></tr>

                    <tr><td colspan="2"><hr /></td></tr>
                    <tr><td colspan="2"><b>Cost Breakdown:</b></td></tr>

                    <tr><td><b>Server Count Cost:</b></td><td>$${formData.costFactors?.serverCount || "Not calculated"}</td></tr>
                    <tr><td><b>Data Capacity Cost:</b></td><td>$${formData.costFactors?.dataCapacity || "Not calculated"}</td></tr>
                    <tr><td><b>High Availability Cost:</b></td><td>$${formData.costFactors?.highAvailability || "Not calculated"}</td></tr>
                    <tr><td><b>Environments Cost:</b></td><td>$${formData.costFactors?.environments || "Not calculated"}</td></tr>
                    <tr><td><b>Compliance Cost:</b></td><td>$${formData.costFactors?.compliance || "Not calculated"}</td></tr>
                    <tr><td><b>Migration Strategy Cost:</b></td><td>$${formData.costFactors?.migrationStrategy || "Not calculated"}</td></tr>
                    <tr><td><b>Auto Scaling Cost:</b></td><td>$${formData.costFactors?.autoScaling || "Not calculated"}</td></tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
  `;

  const textBody = `
Cloud Migration Cost Calculator Submission

Name: ${formData.firstName} ${formData.lastName}
Email: ${formData.emailAddress}
Phone: ${formData.phoneNumber || "Not provided"}
Company: ${formData.companyName || "Not provided"}
Total Cost: $${formData.totalCost || "Not calculated"}
Lower Range: $${formData.lowerRange || "Not calculated"}
Upper Range: $${formData.upperRange || "Not calculated"}
Source: ${formData.secondary_source || "Unknown"}
Submission Time: ${new Date().toLocaleString()}

This email was generated automatically from your cloud migration cost calculator form.
  `;

  return { subject, htmlBody, textBody };
};

/**
 * Generate failure notification email template
 * @param {Object} formData - Form submission data
 * @returns {Object} - Email subject and HTML body
 */
export const generateFailureEmail = (formData) => {
  const subject = `Form Submission Failure Alert - ${formData.firstName} ${formData.lastName}`;

  const htmlBody = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Form Submission Failure</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #ffe6e6; padding: 20px; text-align: center; border-left: 4px solid #dc3545; }
            .content { padding: 20px; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #555; }
            .value { margin-top: 5px; }
            .alert { background-color: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin: 15px 0; }
            .footer { background-color: #f4f4f4; padding: 15px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h2>⚠️ Form Submission Failure Alert</h2>
            </div>
            <div class="content">
                <div class="alert">
                    <strong>Failed Source:</strong> ${formData.failed_source || "Unknown"}
                </div>
                <div class="field">
                    <div class="label">Name:</div>
                    <div class="value">${formData.firstName} ${formData.lastName}</div>
                </div>
                <div class="field">
                    <div class="label">Email:</div>
                    <div class="value">${formData.emailAddress}</div>
                </div>
                <div class="field">
                    <div class="label">Phone:</div>
                    <div class="value">${formData.phoneNumber || "Not provided"}</div>
                </div>
                <div class="field">
                    <div class="label">Company:</div>
                    <div class="value">${formData.companyName || "Not provided"}</div>
                </div>
                <div class="field">
                    <div class="label">Page:</div>
                    <div class="value">${formData.page_name || "Unknown"}</div>
                </div>
                <div class="field">
                    <div class="label">Failure Time:</div>
                    <div class="value">${new Date().toLocaleString()}</div>
                </div>
            </div>
            <div class="footer">
                <p>This is an automated alert for a failed form submission. Please investigate and follow up manually.</p>
            </div>
        </div>
    </body>
    </html>
  `;

  const textBody = `
⚠️ FORM SUBMISSION FAILURE ALERT

Failed Source: ${formData.failed_source || "Unknown"}

Name: ${formData.firstName} ${formData.lastName}
Email: ${formData.emailAddress}
Phone: ${formData.phoneNumber || "Not provided"}
Company: ${formData.companyName || "Not provided"}
Page: ${formData.page_name || "Unknown"}
Failure Time: ${new Date().toLocaleString()}

This is an automated alert for a failed form submission. Please investigate and follow up manually.
  `;

  return { subject, htmlBody, textBody };
};
